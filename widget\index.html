<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <title>Feedback Widget Demo</title>

    <!-- Preload widget CSS for better performance -->
    <link rel="preload" href="widget.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="widget.css"></noscript>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 40px;
            background: #f8fafc;
        }
        
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .subtitle {
            color: #6b7280;
            margin-bottom: 32px;
            font-size: 18px;
        }
        
        .demo-section {
            margin-bottom: 32px;
            padding: 24px;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .demo-section h2 {
            color: #374151;
            margin-bottom: 16px;
            font-size: 20px;
        }
        
        .demo-section p {
            color: #6b7280;
            margin-bottom: 16px;
        }
        
        .demo-controls {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 10px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .demo-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .demo-btn.primary {
            background: #00C2A8;
            color: white;
            border-color: #00C2A8;
        }
        
        .demo-btn.primary:hover {
            background: #00a58e;
            border-color: #00a58e;
        }
        
        .config-section {
            background: #fff;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .config-row {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .config-row label {
            min-width: 120px;
            font-weight: 500;
            color: #374151;
        }
        
        .config-row select,
        .config-row input {
            padding: 6px 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            overflow-x: auto;
            margin-top: 16px;
        }
        
        .highlight {
            background: #fef3c7;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Feedback Widget Demo</h1>
        <p class="subtitle">Test the embeddable feedback widget with different configurations</p>
        
        <div class="demo-section">
            <h2>Widget Controls</h2>
            <p>Use these buttons to test the widget functionality:</p>
            <div class="demo-controls">
                <button class="demo-btn primary" onclick="FeedbackWidget.open()">Open Widget</button>
                <button class="demo-btn" onclick="FeedbackWidget.close()">Close Widget</button>
                <button class="demo-btn" onclick="reinitializeWidget()">Reinitialize</button>
                <button class="demo-btn" onclick="debugWidget()">Debug Widget</button>
            </div>
            <div id="debug-info" style="margin-top: 16px; padding: 12px; background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px; font-family: monospace; font-size: 12px; display: none;">
                <strong>Debug Information:</strong><br>
                <div id="debug-content"></div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>Configuration Options</h2>
            <p>Customize the widget appearance and behavior:</p>
            
            <div class="config-section">
                <div class="config-row">
                    <label>Position:</label>
                    <select id="position">
                        <option value="bottom-right">Bottom Right</option>
                        <option value="bottom-left">Bottom Left</option>
                        <option value="top-right">Top Right</option>
                        <option value="top-left">Top Left</option>
                    </select>
                </div>
                
                <div class="config-row">
                    <label>Primary Color:</label>
                    <input type="color" id="primaryColor" value="#00C2A8">
                </div>
                
                <div class="config-row">
                    <label>Title:</label>
                    <input type="text" id="title" value="Feedback" placeholder="Widget title">
                </div>
                
                <div class="config-row">
                    <label>Placeholder:</label>
                    <input type="text" id="placeholder" value="Share your feedback..." placeholder="Comment placeholder">
                </div>
                
                <div class="config-row">
                    <label>Show Name Field:</label>
                    <input type="checkbox" id="showName" checked>
                </div>
                
                <div class="config-row">
                    <label>Show Email Field:</label>
                    <input type="checkbox" id="showEmail" checked>
                </div>
                
                <button class="demo-btn primary" onclick="updateWidget()">Apply Changes</button>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>Integration Code</h2>
            <p>Copy this code to embed the widget on any website:</p>
            
            <div class="code-block">
&lt;!-- Add this before closing &lt;/body&gt; tag --&gt;<br>
&lt;script src="<span class="highlight">path/to/widget.js</span>"&gt;&lt;/script&gt;<br>
&lt;script&gt;<br>
&nbsp;&nbsp;FeedbackWidget.init({<br>
&nbsp;&nbsp;&nbsp;&nbsp;position: '<span id="code-position">bottom-right</span>',<br>
&nbsp;&nbsp;&nbsp;&nbsp;primaryColor: '<span id="code-color">#00C2A8</span>',<br>
&nbsp;&nbsp;&nbsp;&nbsp;title: '<span id="code-title">Feedback</span>',<br>
&nbsp;&nbsp;&nbsp;&nbsp;placeholder: '<span id="code-placeholder">Share your feedback...</span>',<br>
&nbsp;&nbsp;&nbsp;&nbsp;showName: <span id="code-name">true</span>,<br>
&nbsp;&nbsp;&nbsp;&nbsp;showEmail: <span id="code-email">true</span><br>
&nbsp;&nbsp;});<br>
&lt;/script&gt;
            </div>
        </div>
        
        <div class="demo-section">
            <h2>Features Implemented</h2>
            <ul style="color: #374151; line-height: 1.8;">
                <li>✅ Self-contained JavaScript widget with namespace isolation</li>
                <li>✅ Floating feedback button with customizable position</li>
                <li>✅ Slide-out feedback form panel with smooth animations</li>
                <li>✅ Form fields: Comment (required), Name (optional), Email (optional)</li>
                <li>✅ Auto-detection of website domain</li>
                <li>✅ Responsive design for mobile and desktop</li>
                <li>✅ Dark theme support</li>
                <li>✅ <strong>Enhanced keyboard accessibility (ESC to close, focus trapping)</strong></li>
                <li>✅ <strong>Improved click outside to close functionality</strong></li>
                <li>✅ <strong>Body scroll prevention when widget is open</strong></li>
                <li>✅ <strong>Smooth toggle animations with cubic-bezier easing</strong></li>
                <li>✅ <strong>ARIA attributes for screen readers</strong></li>
                <li>✅ <strong>Focus management and restoration</strong></li>
                <li>✅ <strong>Custom events (feedbackWidget:opened, feedbackWidget:closed)</strong></li>
                <li>✅ Loading states and success/error messages</li>
                <li>✅ CSS reset to prevent host site style conflicts</li>
                <li>✅ Customizable colors, position, and text</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>Accessibility Features</h2>
            <ul style="color: #374151; line-height: 1.8;">
                <li>🔍 <strong>Screen Reader Support:</strong> ARIA labels, roles, and properties</li>
                <li>⌨️ <strong>Keyboard Navigation:</strong> Tab, Shift+Tab, and ESC key support</li>
                <li>🎯 <strong>Focus Management:</strong> Focus trapping within widget when open</li>
                <li>🔄 <strong>Focus Restoration:</strong> Returns focus to trigger element when closed</li>
                <li>🚫 <strong>Scroll Prevention:</strong> Prevents background scrolling when widget is open</li>
                <li>📱 <strong>Mobile Friendly:</strong> Touch-friendly targets and responsive design</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>Event Monitoring</h2>
            <p>The widget dispatches custom events that you can listen to:</p>
            <div id="event-log" style="background: #f3f4f6; padding: 12px; border-radius: 6px; font-family: monospace; font-size: 12px; max-height: 150px; overflow-y: auto;">
                <div style="color: #6b7280;">Event log will appear here...</div>
            </div>
        </div>
    </div>

    <!-- Load the feedback widget -->
    <script src="widget.js"></script>
    <script>
        // Add debugging information
        console.log('Document ready state:', document.readyState);
        console.log('FeedbackWidget available:', typeof FeedbackWidget);

        // Initialize the widget with default settings
        try {
            FeedbackWidget.init({
                position: 'bottom-right',
                primaryColor: '#00C2A8',
                title: 'Feedback',
                placeholder: 'Share your feedback...',
                showName: true,
                showEmail: true
            });
            console.log('Widget initialization completed');
        } catch (error) {
            console.error('Widget initialization failed:', error);
        }
        
        // Demo functions
        function updateWidget() {
            const position = document.getElementById('position').value;
            const primaryColor = document.getElementById('primaryColor').value;
            const title = document.getElementById('title').value;
            const placeholder = document.getElementById('placeholder').value;
            const showName = document.getElementById('showName').checked;
            const showEmail = document.getElementById('showEmail').checked;
            
            // Remove existing widget
            const existingContainer = document.getElementById('feedback-widget-container');
            if (existingContainer) {
                existingContainer.remove();
            }
            
            // Remove existing styles
            const existingStyles = document.getElementById('feedback-widget-styles');
            if (existingStyles) {
                existingStyles.remove();
            }
            
            // Reinitialize with new config
            FeedbackWidget.init({
                position,
                primaryColor,
                title,
                placeholder,
                showName,
                showEmail
            });
            
            // Update code example
            updateCodeExample();
        }
        
        function reinitializeWidget() {
            updateWidget();
        }

        function debugWidget() {
            const debugInfo = document.getElementById('debug-info');
            const debugContent = document.getElementById('debug-content');

            const container = document.getElementById('feedback-widget-container');
            const button = document.getElementById('feedback-widget-button');
            const panel = document.getElementById('feedback-widget-panel');
            const styles = document.getElementById('feedback-widget-styles');

            let info = '';
            info += `Container exists: ${!!container}<br>`;
            info += `Button exists: ${!!button}<br>`;
            info += `Panel exists: ${!!panel}<br>`;
            info += `Styles injected: ${!!styles}<br>`;

            if (container) {
                info += `Container classes: ${container.className}<br>`;
                info += `Container theme: ${container.getAttribute('data-theme')}<br>`;
            }

            if (button) {
                info += `Button classes: ${button.className}<br>`;
                const buttonStyles = window.getComputedStyle(button);
                info += `Button position: ${buttonStyles.position}<br>`;
                info += `Button z-index: ${buttonStyles.zIndex}<br>`;
                info += `Button background: ${buttonStyles.backgroundColor}<br>`;
            }

            info += `Widget is open: ${FeedbackWidget.isOpen ? FeedbackWidget.isOpen() : 'Unknown'}<br>`;

            debugContent.innerHTML = info;
            debugInfo.style.display = 'block';
        }
        
        function updateCodeExample() {
            document.getElementById('code-position').textContent = document.getElementById('position').value;
            document.getElementById('code-color').textContent = document.getElementById('primaryColor').value;
            document.getElementById('code-title').textContent = document.getElementById('title').value;
            document.getElementById('code-placeholder').textContent = document.getElementById('placeholder').value;
            document.getElementById('code-name').textContent = document.getElementById('showName').checked;
            document.getElementById('code-email').textContent = document.getElementById('showEmail').checked;
        }
        
        // Initialize code example
        updateCodeExample();

        // Set up event monitoring
        setupEventMonitoring();

        function setupEventMonitoring() {
            const eventLog = document.getElementById('event-log');

            function logEvent(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.style.color = '#374151';
                logEntry.innerHTML = `<span style="color: #6b7280;">[${timestamp}]</span> ${message}`;

                // Clear placeholder text
                if (eventLog.children.length === 1 && eventLog.children[0].textContent.includes('Event log will appear here')) {
                    eventLog.innerHTML = '';
                }

                eventLog.appendChild(logEntry);
                eventLog.scrollTop = eventLog.scrollHeight;

                // Keep only last 10 entries
                while (eventLog.children.length > 10) {
                    eventLog.removeChild(eventLog.firstChild);
                }
            }

            // Listen for widget events
            window.addEventListener('feedbackWidget:opened', function(e) {
                logEvent('🟢 Widget opened');
                console.log('Feedback widget opened:', e.detail);
            });

            window.addEventListener('feedbackWidget:closed', function(e) {
                logEvent('🔴 Widget closed');
                console.log('Feedback widget closed:', e.detail);
            });

            // Log initial state
            logEvent('📡 Event monitoring started');
        }
    </script>
</body>
</html>
